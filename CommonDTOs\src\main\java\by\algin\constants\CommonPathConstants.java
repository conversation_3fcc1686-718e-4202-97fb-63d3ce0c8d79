package by.algin.constants;

public final class CommonPathConstants {

    // Auth paths
    public static final String AUTH_LOGIN = "/auth/login";
    public static final String AUTH_REGISTER = "/auth/register";
    public static final String AUTH_CONFIRM = "/auth/confirm";
    public static final String AUTH_LOGOUT = "/auth/logout";
    public static final String AUTH_REGISTRATION_SUCCESS = "/auth/registration-success";
    public static final String AUTH_TOKEN_EXPIRED = "/auth/token-expired";
    public static final String AUTH_RESEND_CONFIRMATION = "/auth/resend-confirmation";
    public static final String AUTH_ACCOUNT_CONFIRMED = "/auth/account-confirmed";

    // API base paths
    public static final String API_PATH = "/api";
    public static final String API_AUTH_PATH = "/api/auth";
    public static final String API_AUTH_BASE = "/api/auth";
    public static final String API_AUTH_VALIDATE_TOKEN = "/validate-token";
    public static final String API_AUTH_REGISTER = "/api/auth/register";
    public static final String API_AUTH_LOGIN = "/api/auth/login";
    public static final String API_AUTH_REFRESH_TOKEN = "/api/auth/refresh-token";
    public static final String API_AUTH_CONFIRM = "/api/auth/confirm";
    public static final String API_AUTH_RESEND_CONFIRMATION = "/api/auth/resend-confirmation";
    public static final String API_AUTH_VALIDATE = "/auth/validate";

    // User API paths
    public static final String API_USERS = "/api/users";
    public static final String API_USERS_SEARCH = "/users/search";
    public static final String API_USERS_BY_ID = "/users/{userId}";
    public static final String API_USERS_EXISTS = "/users/{userId}/exists";
    public static final String API_USERS_BY_USERNAME = "/api/users/by-username/{username}";
    public static final String API_USERS_BY_EMAIL = "/api/users/by-email/{email}";

    // Project API paths
    public static final String API_PROJECTS_PATH = "/api/projects";
    public static final String API_PROJECTS = "/projects";
    public static final String API_PROJECTS_CREATE = "/projects";
    public static final String API_PROJECTS_BY_ID = "/projects/{projectId}";
    public static final String API_PROJECTS_UPDATE = "/projects/{projectId}";
    public static final String API_PROJECTS_DELETE = "/projects/{projectId}";
    public static final String API_PROJECTS_BY_USER = "/user";
    public static final String API_PROJECTS_BY_USER_ID = "/user/{userId}";
    public static final String API_PROJECTS_MEMBERS = "/projects/{projectId}/members";
    public static final String API_PROJECTS_MEMBERS_ROLE = "/projects/{projectId}/members/{userId}/role";
    public static final String API_PROJECTS_MEMBERS_DELETE = "/projects/{projectId}/members/{userId}";

    // Project invitations API paths
    public static final String API_PROJECTS_INVITATIONS = "/projects/{projectId}/invitations";
    public static final String API_INVITATIONS_BY_TOKEN = "/invitations/{token}";
    public static final String API_INVITATIONS_ACCEPT = "/invitations/{token}/accept";
    // API_INVITATIONS_REJECT удален - новая система использует только принятие инвайтов
    public static final String API_PROJECTS_INVITATIONS_CANCEL = "/projects/{projectId}/invitations/{invitationId}";

    // WebUI API paths removed - WebUIService should use standard API endpoints

    // Status transitions API paths
    public static final String API_STATUS_TRANSITIONS_BASE = "/api/v1/projects/status-transitions";
    public static final String STATUS_TRANSITIONS_BY_STATUS = "/{status}";
    public static final String STATUS_TRANSITIONS_VALIDATE = "/validate/{from}/{to}";
    public static final String STATUS_TRANSITIONS_DESCRIPTION = "/description";
    public static final String STATUS_TRANSITIONS_VALIDATE_CONFIG = "/validate-configuration";

    // Common endpoints
    public static final String LOGIN_ENDPOINT = "/login";
    public static final String REGISTER_ENDPOINT = "/register";
    public static final String CONFIRM_ENDPOINT = "/confirm";
    public static final String RESEND_CONFIRMATION_ENDPOINT = "/resend-confirmation";
    public static final String EMAIL_BY_TOKEN_ENDPOINT = "/email-by-token";
    public static final String LOGOUT_ENDPOINT = "/logout";
    public static final String PROJECTS_ENDPOINT = "/projects";
    public static final String MEMBERS_ENDPOINT = "/members";
    public static final String ROLE_ENDPOINT = "/role";
    public static final String ROLES_ENDPOINT = "/roles";
    public static final String ROLES_ASSIGNABLE_ENDPOINT = "/roles/assignable";
    public static final String USER_ENDPOINT = "/user";
    public static final String TEST_ENDPOINT = "/test";
    public static final String HEALTH_ENDPOINT = "/health";
    public static final String SEARCH_ENDPOINT = "/search";
    public static final String VALIDATE_ENDPOINT = "/validate";
    public static final String REFRESH_TOKEN_ENDPOINT = "/refresh-token";
    public static final String CREATE_ENDPOINT = "/create";
    public static final String JOIN_ENDPOINT = "/join";

    // Path variables
    public static final String PATH_VAR_PROJECT_ID = "/{projectId}";
    public static final String PATH_VAR_USER_ID = "/{userId}";
    public static final String PATH_VAR_PROJECT_ID_MEMBERS = "/{projectId}/members";
    public static final String PATH_VAR_PROJECT_ID_USER_ID = "/{projectId}/members/{userId}";
    public static final String PATH_VAR_PROJECT_ID_USER_ID_ROLE = "/{projectId}/members/{userId}/role";
    public static final String PATH_VAR_USER_PROJECTS = "/user/{userId}";
    public static final String PROJECT_ID_ENDPOINT = "/{projectId}";
    public static final String PROJECT_EDIT_ENDPOINT = "/{projectId}/edit";
    public static final String PROJECT_DELETE_ENDPOINT = "/{projectId}/delete";
    public static final String PROJECT_MEMBERS_ENDPOINT = "/{projectId}/members";
    public static final String PROJECT_INVITE_ENDPOINT = "/{projectId}/invite";
    public static final String MEMBERS_REMOVE_ENDPOINT = "/{userId}/remove";

    // Request parameters
    public static final String PARAM_FIELD = "field";
    public static final String PARAM_VALUE = "value";
    public static final String PARAM_TOKEN = "token";
    public static final String PARAM_EMAIL = "email";
    public static final String PARAM_PROJECT_ID = "projectId";
    public static final String PARAM_USER_ID = "userId";
    public static final String PARAM_ROLE = "role";
    public static final String PARAM_STATUS = "status";
    public static final String PARAM_NAME = "name";
    public static final String PARAM_DESCRIPTION = "description";

    // Request parameters for pagination
    public static final String REQUEST_PARAM_USER_ID = "userId";
    public static final String REQUEST_PARAM_STATUS = "status";
    public static final String REQUEST_PARAM_PAGE = "page";
    public static final String REQUEST_PARAM_SIZE = "size";

    // Headers
    public static final String AUTHORIZATION_HEADER = "Authorization";
    public static final String BEARER_PREFIX = "Bearer ";

    // Cookie names
    public static final String JWT_COOKIE_NAME = "jwt";

    private CommonPathConstants() {
    }
}
