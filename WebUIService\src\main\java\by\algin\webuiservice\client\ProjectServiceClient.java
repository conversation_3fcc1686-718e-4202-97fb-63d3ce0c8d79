package by.algin.webuiservice.client;

import by.algin.constants.CommonPathConstants;
import by.algin.constants.CommonServiceConstants;
import by.algin.dto.project.*;
import by.algin.dto.response.ApiResponse;
import by.algin.dto.response.PagedResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(
    name = CommonServiceConstants.PROJECT_SERVICE,
    contextId = "${app.feign-clients.project-service.context-id}",
    path = "${app.feign-clients.project-service.path}"
)
public interface ProjectServiceClient {

    String PROJECTS_BASE = "/projects";

    @PostMapping(PROJECTS_BASE)
    ProjectResponse createProject(@RequestBody CreateProjectRequest request);

    @GetMapping(PROJECTS_BASE + "/{projectId}")
    ProjectResponse getProject(@PathVariable("projectId") Long projectId);

    @PutMapping(PROJECTS_BASE + "/{projectId}")
    ProjectResponse updateProject(@PathVariable("projectId") Long projectId, @RequestBody UpdateProjectRequest request);

    @DeleteMapping(PROJECTS_BASE + "/{projectId}")
    void deleteProject(@PathVariable("projectId") Long projectId);

    @GetMapping(PROJECTS_BASE + "/user")
    PagedResponse<ProjectResponse> getUserProjects(@RequestParam("username") String username);

    @PostMapping(PROJECTS_BASE + "/{projectId}/members")
    ProjectMemberResponse addProjectMember(@PathVariable("projectId") Long projectId, @RequestBody AddProjectMemberRequest request);

    @PutMapping(PROJECTS_BASE + "/{projectId}/members/{userId}/role")
    ProjectMemberResponse updateProjectMemberRole(@PathVariable("projectId") Long projectId,
                                                 @PathVariable("userId") Long userId,
                                                 @RequestBody UpdateProjectMemberRoleRequest request);

    @DeleteMapping(PROJECTS_BASE + "/{projectId}/members/{userId}")
    void removeProjectMember(@PathVariable("projectId") Long projectId, @PathVariable("userId") Long userId);

    @GetMapping(PROJECTS_BASE + "/{projectId}/members")
    ProjectMemberListResponse getProjectMembers(@PathVariable("projectId") Long projectId);

    @PostMapping(PROJECTS_BASE + "/{projectId}/invitations")
    InvitationResponse createInvitation(@PathVariable("projectId") Long projectId, @RequestBody CreateInvitationRequest request);

    @GetMapping("/invitations/{token}")
    InvitationResponse getInvitationByToken(@PathVariable("token") String token);

    @PostMapping("/invitations/accept")
    ProjectMemberResponse acceptInvitation(@PathVariable("token") String token);

    @GetMapping(PROJECTS_BASE + "/roles")
    ApiResponse<List<ProjectRoleResponse>> getAvailableRoles();

    @GetMapping(PROJECTS_BASE + "/roles/assignable")
    ApiResponse<List<ProjectRoleResponse>> getAssignableRoles();

}

